<?php
/**
 * Test file to verify admin integration
 * Run this from WordPress admin to test the setup wizard integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

echo "<h2>Price by Country Setup Wizard Integration Test</h2>";

// Test 1: Check if setup wizard class exists
echo "<h3>1. Setup Wizard Class Check:</h3>";
if (class_exists('PBC_Setup_Wizard')) {
    echo "✓ PBC_Setup_Wizard class exists<br>";
    
    // Test setup URL
    $setup_url = PBC_Setup_Wizard::get_setup_url();
    echo "✓ Setup URL: " . esc_html($setup_url) . "<br>";
    
    // Test should_show_setup
    $should_show = PBC_Setup_Wizard::should_show_setup();
    echo "✓ Should show setup: " . ($should_show ? 'Yes' : 'No') . "<br>";
    
} else {
    echo "✗ PBC_Setup_Wizard class not found<br>";
}

// Test 2: Check admin menu integration
echo "<h3>2. Admin Menu Integration:</h3>";
global $submenu;
if (isset($submenu['woocommerce'])) {
    $found_setup = false;
    foreach ($submenu['woocommerce'] as $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'pbc-setup-wizard') {
            echo "✓ Setup Wizard menu item found: " . esc_html($menu_item[0]) . "<br>";
            $found_setup = true;
            break;
        }
    }
    if (!$found_setup) {
        echo "✗ Setup Wizard menu item not found in WooCommerce submenu<br>";
    }
} else {
    echo "✗ WooCommerce submenu not found<br>";
}

// Test 3: Check CSS and JS files
echo "<h3>3. Asset Files Check:</h3>";
$css_file = PBC_PLUGIN_DIR . 'admin/css/pbc-setup.css';
$js_file = PBC_PLUGIN_DIR . 'admin/js/pbc-setup.js';

if (file_exists($css_file)) {
    echo "✓ CSS file exists: " . esc_html($css_file) . "<br>";
} else {
    echo "✗ CSS file missing: " . esc_html($css_file) . "<br>";
}

if (file_exists($js_file)) {
    echo "✓ JS file exists: " . esc_html($js_file) . "<br>";
} else {
    echo "✗ JS file missing: " . esc_html($js_file) . "<br>";
}

// Test 4: Check setup completion status
echo "<h3>4. Setup Status:</h3>";
$setup_completed = get_option('pbc_setup_completed', false);
echo "Setup completed: " . ($setup_completed ? 'Yes' : 'No') . "<br>";

if (!$setup_completed) {
    echo "<p><a href='" . esc_url(PBC_Setup_Wizard::get_setup_url()) . "' class='button button-primary'>Go to Setup Wizard</a></p>";
}

// Test 5: Check WordPress version compatibility
echo "<h3>5. WordPress Compatibility:</h3>";
global $wp_version;
echo "WordPress version: " . esc_html($wp_version) . "<br>";
echo "Required: 5.0+<br>";
if (version_compare($wp_version, '5.0', '>=')) {
    echo "✓ WordPress version compatible<br>";
} else {
    echo "✗ WordPress version too old<br>";
}

echo "<hr>";
echo "<p><strong>Integration Test Complete</strong></p>";
echo "<p>If all tests pass, the setup wizard should be accessible from WooCommerce > Setup Wizard in the admin menu.</p>";
