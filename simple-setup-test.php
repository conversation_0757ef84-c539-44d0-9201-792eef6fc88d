<?php
/**
 * Simple Setup Test
 * A minimal version to test form submission and step progression
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current step
$current_step = isset($_GET['step']) ? sanitize_key($_GET['step']) : 'country_detection';
$steps = array('country_detection', 'sample_rules', 'ready');

echo "<div class='wrap'>";
echo "<h1>Simple Setup Test - Step: " . esc_html($current_step) . "</h1>";

// Handle form submission
if (!empty($_POST['save_step'])) {
    echo "<div class='notice notice-info'>";
    echo "<p><strong>Form submitted!</strong></p>";
    echo "<p>Posted step: " . esc_html($_POST['save_step']) . "</p>";
    echo "<p>Current step: " . esc_html($current_step) . "</p>";
    echo "</div>";
    
    // Get next step
    $current_index = array_search($current_step, $steps);
    if ($current_index !== false && $current_index < count($steps) - 1) {
        $next_step = $steps[$current_index + 1];
        $next_url = admin_url('admin.php?page=pbc-setup-wizard&step=' . $next_step);
        
        echo "<div class='notice notice-success'>";
        echo "<p><strong>Redirecting to next step...</strong></p>";
        echo "<p>Next step: " . esc_html($next_step) . "</p>";
        echo "<p>Next URL: <a href='" . esc_url($next_url) . "'>" . esc_html($next_url) . "</a></p>";
        echo "</div>";
        
        // JavaScript redirect for testing
        echo "<script>setTimeout(function() { window.location.href = '" . esc_js($next_url) . "'; }, 2000);</script>";
    } else {
        echo "<div class='notice notice-success'>";
        echo "<p><strong>Setup complete!</strong></p>";
        echo "</div>";
    }
}

// Show current step form
if ($current_step === 'country_detection') {
    echo "<h2>Country Detection Settings</h2>";
    echo "<form method='post'>";
    echo "<table class='form-table'>";
    echo "<tr>";
    echo "<th><label for='detection_method'>Detection Method:</label></th>";
    echo "<td>";
    echo "<select name='detection_method' id='detection_method'>";
    echo "<option value='auto'>Automatic</option>";
    echo "<option value='ip'>IP Address</option>";
    echo "</select>";
    echo "</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<th><label for='default_country'>Default Country:</label></th>";
    echo "<td>";
    echo "<select name='default_country' id='default_country'>";
    echo "<option value='US'>United States</option>";
    echo "<option value='GB'>United Kingdom</option>";
    echo "</select>";
    echo "</td>";
    echo "</tr>";
    echo "</table>";
    echo "<p class='submit'>";
    echo "<button type='submit' name='save_step' value='country_detection' class='button button-primary'>Continue to Sample Rules</button>";
    echo "</p>";
    echo "</form>";
    
} elseif ($current_step === 'sample_rules') {
    echo "<h2>Sample Rules</h2>";
    echo "<form method='post'>";
    echo "<table class='form-table'>";
    echo "<tr>";
    echo "<th><label for='create_samples'>Create Samples:</label></th>";
    echo "<td>";
    echo "<label><input type='checkbox' name='create_samples' id='create_samples' value='1' checked> Yes, create sample rules</label>";
    echo "</td>";
    echo "</tr>";
    echo "</table>";
    echo "<p class='submit'>";
    echo "<button type='submit' name='save_step' value='sample_rules' class='button button-primary'>Continue to Ready</button>";
    echo "</p>";
    echo "</form>";
    
} elseif ($current_step === 'ready') {
    echo "<h2>Setup Complete!</h2>";
    echo "<p>Your setup is now complete.</p>";
    echo "<p><a href='" . esc_url(admin_url()) . "' class='button button-primary'>Go to Dashboard</a></p>";
}

// Debug info
echo "<hr>";
echo "<h3>Debug Information:</h3>";
echo "<ul>";
echo "<li><strong>Current Step:</strong> " . esc_html($current_step) . "</li>";
echo "<li><strong>Available Steps:</strong> " . implode(', ', $steps) . "</li>";
echo "<li><strong>GET Parameters:</strong> " . esc_html(print_r($_GET, true)) . "</li>";
if (!empty($_POST)) {
    echo "<li><strong>POST Data:</strong> " . esc_html(print_r($_POST, true)) . "</li>";
}
echo "</ul>";

// Step navigation links
echo "<h3>Manual Step Navigation:</h3>";
echo "<p>";
foreach ($steps as $step) {
    $url = admin_url('admin.php?page=pbc-setup-wizard&step=' . $step);
    $class = ($step === $current_step) ? 'button button-primary' : 'button';
    echo "<a href='" . esc_url($url) . "' class='" . esc_attr($class) . "'>" . esc_html(ucfirst(str_replace('_', ' ', $step))) . "</a> ";
}
echo "</p>";

echo "</div>";
