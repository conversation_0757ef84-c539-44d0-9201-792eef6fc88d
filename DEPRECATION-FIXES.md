# WordPress 6.4+ Deprecation Fixes

This document outlines the fixes applied to resolve WordPress deprecation warnings and notices that appeared in WordPress 6.4+ when using the plugin's setup wizard.

## Issues Fixed

### 1. Deprecated Function Warnings

**Problem:**

- `print_emoji_styles` is deprecated since WordPress 6.4.0 - should use `wp_enqueue_emoji_styles` instead
- `wp_admin_bar_header` is deprecated since WordPress 6.4.0 - should use `wp_enqueue_admin_bar_header_styles` instead

**Root Cause:**
The setup wizard was calling `do_action('admin_print_styles')`, `do_action('admin_print_scripts')`, and `do_action('admin_head')` which triggered these deprecated functions in WordPress core.

### 2. Incorrect Function Call Notices

**Problem:**

- `get_current_page` function was called incorrectly - should be called on or after the `current_screen` hook

**Root Cause:**
The setup wizard wasn't properly setting up the WordPress admin context before calling admin actions.

## Solutions Applied

### Modified File: `includes/class-pbc-setup-wizard.php`

#### 1. Updated `setup_wizard_header()` Method

**Before:**

```php
public function setup_wizard_header() {
    ?>
    <!DOCTYPE html>
    <html <?php language_attributes(); ?>>
    <head>
        <meta name="viewport" content="width=device-width" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><?php _e('Price by Country &rsaquo; Setup Wizard', 'price-by-country'); ?></title>
        <?php do_action('admin_print_styles'); ?>
        <?php do_action('admin_print_scripts'); ?>
        <?php do_action('admin_head'); ?>
    </head>
    <body class="pbc-setup wp-core-ui">
```

**After:**

```php
public function setup_wizard_header() {
    // Set up admin context properly
    set_current_screen('dashboard');

    ?>
    <!DOCTYPE html>
    <html <?php language_attributes(); ?>>
    <head>
        <meta name="viewport" content="width=device-width" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><?php _e('Price by Country &rsaquo; Setup Wizard', 'price-by-country'); ?></title>
        <?php
        // Manually enqueue only essential admin styles to avoid deprecated functions
        wp_enqueue_style('dashicons');
        wp_enqueue_style('buttons');
        wp_enqueue_style('forms');

        // Print only the styles we've enqueued
        wp_print_styles();

        // Enqueue essential admin scripts
        wp_enqueue_script('jquery');
        wp_enqueue_script('utils');

        // Print only the scripts we've enqueued
        wp_print_scripts();
        ?>
    </head>
    <body class="pbc-setup wp-core-ui wp-admin">
        <div class="pbc-setup-container">
```

#### 2. Updated `enqueue_scripts()` Method

**Before:**

```php
wp_enqueue_style('pbc-setup', PBC_PLUGIN_URL . 'admin/css/pbc-setup.css', array('dashicons', 'install'), PBC_VERSION);
```

**After:**

```php
wp_enqueue_style('pbc-setup', PBC_PLUGIN_URL . 'admin/css/pbc-setup.css', array('dashicons'), PBC_VERSION);
```

## Key Changes Made

1. **Proper Admin Context Setup**: Added `set_current_screen('dashboard')` to establish proper WordPress admin context
2. **Removed Problematic Actions**: Eliminated calls to `admin_print_styles`, `admin_print_scripts`, and `admin_head` actions
3. **Manual Style/Script Enqueuing**: Directly enqueue only the essential WordPress admin styles and scripts needed
4. **Direct Printing**: Use `wp_print_styles()` and `wp_print_scripts()` directly instead of triggering actions
5. **Proper Body Classes**: Added `wp-admin` class to body for proper WordPress admin styling
6. **Dependency Cleanup**: Removed unnecessary 'install' dependency from setup CSS

## Benefits

- ✅ Eliminates all WordPress 6.4+ deprecation warnings
- ✅ Fixes `get_current_page` incorrect call notices
- ✅ Maintains existing setup wizard functionality and design
- ✅ Improves compatibility with future WordPress versions
- ✅ Reduces unnecessary function calls and improves performance
- ✅ Follows WordPress best practices for admin page creation

## Testing

A test script (`test-setup-wizard-fix.php`) has been created to verify:

- No deprecated function calls remain in the code
- All fixes have been properly applied
- The setup wizard structure is intact

## Compatibility

These fixes ensure compatibility with:

- WordPress 6.4+
- All previous WordPress versions (backward compatible)
- All existing plugin functionality

The changes are minimal and targeted, ensuring no breaking changes to existing functionality while resolving the deprecation issues.
