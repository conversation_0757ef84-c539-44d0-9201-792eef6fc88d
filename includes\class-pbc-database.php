<?php
/**
 * Database operations class for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Database Class
 */
class PBC_Database {

    /**
     * Database version
     */
    const DB_VERSION = '1.0.0';

    /**
     * Database version option name
     */
    const DB_VERSION_OPTION = 'pbc_db_version';

    /**
     * Table names
     */
    private $pricing_rules_table;
    private $country_cache_table;

    /**
     * WordPress database instance
     */
    private $wpdb;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        
        // Set table names with WordPress prefix
        $this->pricing_rules_table = $wpdb->prefix . 'pbc_pricing_rules';
        $this->country_cache_table = $wpdb->prefix . 'pbc_country_cache';
        
        $this->error_handler = PBC_Error_Handler::get_instance();
        $this->logger = PBC_Logger::get_instance();
    }

    /**
     * Initialize database - check version and create/update tables if needed
     */
    public function init() {
        $installed_version = get_option(self::DB_VERSION_OPTION);
        
        if ($installed_version !== self::DB_VERSION) {
            $this->create_tables();
            
            if ($installed_version) {
                $this->update_database($installed_version);
            }
            
            update_option(self::DB_VERSION_OPTION, self::DB_VERSION);
        }
    }

    /**
     * Create database tables
     */
    public function create_tables() {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $charset_collate = $this->wpdb->get_charset_collate();

        // Create pricing rules table with optimized indexes
        $pricing_rules_sql = "CREATE TABLE {$this->pricing_rules_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            rule_type enum('global','category','product') NOT NULL,
            object_id bigint(20) DEFAULT NULL,
            country_code varchar(2) NOT NULL,
            adjustment_type enum('fixed','percentage') NOT NULL,
            adjustment_value decimal(10,4) NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_unique_rule (rule_type, object_id, country_code),
            KEY idx_rule_lookup (rule_type, object_id, country_code),
            KEY idx_country_active (country_code, is_active),
            KEY idx_type_active (rule_type, is_active),
            KEY idx_object_country (object_id, country_code),
            KEY idx_active (is_active),
            KEY idx_created (created_at),
            KEY idx_updated (updated_at)
        ) $charset_collate;";

        // Create country cache table
        $country_cache_sql = "CREATE TABLE {$this->country_cache_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            session_id varchar(255) NOT NULL,
            ip_address varchar(45) NOT NULL,
            country_code varchar(2) NOT NULL,
            detection_method enum('ip','billing','shipping') NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_session (session_id),
            KEY idx_ip (ip_address),
            KEY idx_expires (expires_at)
        ) $charset_collate;";

        // Execute table creation
        dbDelta($pricing_rules_sql);
        dbDelta($country_cache_sql);

        // Log table creation
        error_log('PBC: Database tables created successfully');
    }

    /**
     * Update database schema
     *
     * @param string $old_version Previous version
     */
    public function update_database($old_version) {
        // Handle database migrations based on version
        if (version_compare($old_version, '1.0.0', '<')) {
            // Future migrations will be handled here
            error_log("PBC: Updating database from version {$old_version} to " . self::DB_VERSION);
        }
    }

    /**
     * Drop database tables (used during uninstall)
     */
    public function drop_tables() {
        $this->wpdb->query("DROP TABLE IF EXISTS {$this->pricing_rules_table}");
        $this->wpdb->query("DROP TABLE IF EXISTS {$this->country_cache_table}");
        
        // Remove version option
        delete_option(self::DB_VERSION_OPTION);
        
        error_log('PBC: Database tables dropped successfully');
    }

    /**
     * Clean up expired cache entries
     */
    public function cleanup_expired_cache() {
        $deleted = $this->wpdb->query(
            $this->wpdb->prepare(
                "DELETE FROM {$this->country_cache_table} WHERE expires_at < %s",
                current_time('mysql')
            )
        );
        
        if ($deleted !== false) {
            error_log("PBC: Cleaned up {$deleted} expired cache entries");
        }
        
        return $deleted;
    }

    /**
     * Get table names
     */
    public function get_pricing_rules_table() {
        return $this->pricing_rules_table;
    }

    public function get_country_cache_table() {
        return $this->country_cache_table;
    }

    /**
     * Check if tables exist
     */
    public function tables_exist() {
        $pricing_table_exists = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SHOW TABLES LIKE %s",
                $this->pricing_rules_table
            )
        ) === $this->pricing_rules_table;

        $cache_table_exists = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SHOW TABLES LIKE %s",
                $this->country_cache_table
            )
        ) === $this->country_cache_table;

        return $pricing_table_exists && $cache_table_exists;
    }

    /**
     * Get database statistics
     */
    public function get_stats() {
        $pricing_rules_count = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->pricing_rules_table}"
        );

        $cache_entries_count = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->country_cache_table}"
        );

        $expired_cache_count = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->country_cache_table} WHERE expires_at < %s",
                current_time('mysql')
            )
        );

        return array(
            'pricing_rules' => intval($pricing_rules_count),
            'cache_entries' => intval($cache_entries_count),
            'expired_cache' => intval($expired_cache_count),
            'db_version' => get_option(self::DB_VERSION_OPTION, 'Not installed')
        );
    }

    // ========================================
    // PRICING RULES CRUD OPERATIONS
    // ========================================

    /**
     * Create a new pricing rule
     *
     * @param array $rule_data Rule data
     * @return int|false Rule ID on success, false on failure
     */
    public function create_pricing_rule($rule_data) {
        // Sanitize and validate input
        $sanitized_data = $this->sanitize_pricing_rule_data($rule_data);
        if (!$sanitized_data) {
            return false;
        }

        // Check for existing rule to prevent duplicates
        $existing_rule = $this->get_pricing_rule_by_criteria(
            $sanitized_data['rule_type'],
            $sanitized_data['object_id'],
            $sanitized_data['country_code']
        );

        if ($existing_rule) {
            // Update existing rule instead of creating duplicate
            return $this->update_pricing_rule($existing_rule->id, $sanitized_data);
        }

        // Insert new rule
        $result = $this->wpdb->insert(
            $this->pricing_rules_table,
            $sanitized_data,
            array('%s', '%d', '%s', '%s', '%f', '%d')
        );

        if ($result === false) {
            error_log('PBC: Failed to create pricing rule - ' . $this->wpdb->last_error);
            return false;
        }

        $rule_id = $this->wpdb->insert_id;
        
        // Clear related cache
        $this->clear_pricing_cache($sanitized_data['rule_type'], $sanitized_data['object_id']);
        
        // Trigger cache invalidation
        do_action('pbc_pricing_rule_changed', $sanitized_data['rule_type'], $sanitized_data['object_id'], $sanitized_data['country_code']);
        
        // Log the pricing rule creation
        do_action('pbc_pricing_rule_created', $sanitized_data, $rule_id);
        
        return $rule_id;
    }

    /**
     * Get pricing rule by ID
     *
     * @param int $rule_id Rule ID
     * @return object|null Rule object or null if not found
     */
    public function get_pricing_rule($rule_id) {
        $rule_id = intval($rule_id);
        
        $rule = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->pricing_rules_table} WHERE id = %d",
                $rule_id
            )
        );

        return $rule ?: null;
    }

    /**
     * Get pricing rule by criteria
     *
     * @param string $rule_type Rule type (global, category, product)
     * @param int|null $object_id Object ID (null for global)
     * @param string $country_code Country code
     * @return object|null Rule object or null if not found
     */
    public function get_pricing_rule_by_criteria($rule_type, $object_id, $country_code) {
        try {
            // Validate inputs
            if (empty($rule_type) || empty($country_code)) {
                throw new InvalidArgumentException('Rule type and country code are required');
            }

            $rule = $this->wpdb->get_row(
                $this->wpdb->prepare(
                    "SELECT * FROM {$this->pricing_rules_table} 
                    WHERE rule_type = %s AND object_id = %d AND country_code = %s AND is_active = 1",
                    $rule_type,
                    $object_id,
                    $country_code
                )
            );

            // Check for database errors
            if ($this->wpdb->last_error) {
                throw new Exception("Database error: " . $this->wpdb->last_error);
            }

            return $rule ?: null;
            
        } catch (Exception $e) {
            return $this->error_handler->handle_database_error($e, [
                'operation' => 'get_pricing_rule',
                'rule_type' => $rule_type,
                'object_id' => $object_id,
                'country_code' => $country_code,
                'function' => __FUNCTION__
            ]);
        }
    }

    /**
     * Get pricing rules by type and object
     *
     * @param string $rule_type Rule type
     * @param int|null $object_id Object ID
     * @param bool $active_only Get only active rules
     * @return array Array of rule objects
     */
    public function get_pricing_rules_by_type($rule_type, $object_id = null, $active_only = true) {
        // Check cache first
        $cache_key = 'pbc_rules_' . $rule_type . '_' . ($object_id ?: 'null') . '_' . ($active_only ? '1' : '0');
        $cached_rules = get_transient($cache_key);
        if ($cached_rules !== false) {
            return $cached_rules;
        }

        $where_clause = "WHERE rule_type = %s";
        $params = array($rule_type);

        if ($object_id !== null) {
            $where_clause .= " AND object_id = %d";
            $params[] = $object_id;
        } else {
            $where_clause .= " AND object_id IS NULL";
        }

        if ($active_only) {
            $where_clause .= " AND is_active = 1";
        }

        $rules = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->pricing_rules_table} {$where_clause} ORDER BY country_code",
                $params
            )
        );

        $rules = $rules ?: array();
        
        // Cache the results for 30 minutes
        set_transient($cache_key, $rules, 1800);

        return $rules;
    }

    /**
     * Get all pricing rules for a country
     *
     * @param string $country_code Country code
     * @param bool $active_only Get only active rules
     * @return array Array of rule objects grouped by type
     */
    public function get_pricing_rules_by_country($country_code, $active_only = true) {
        // Check cache first
        $cache_key = 'pbc_country_rules_' . $country_code . '_' . ($active_only ? '1' : '0');
        $cached_rules = get_transient($cache_key);
        if ($cached_rules !== false) {
            return $cached_rules;
        }

        $where_clause = "WHERE country_code = %s";
        $params = array($country_code);

        if ($active_only) {
            $where_clause .= " AND is_active = 1";
        }

        $rules = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->pricing_rules_table} {$where_clause} 
                ORDER BY rule_type, object_id",
                $params
            )
        );

        // Group rules by type for easier processing
        $grouped_rules = array(
            'global' => array(),
            'category' => array(),
            'product' => array()
        );

        foreach ($rules as $rule) {
            $grouped_rules[$rule->rule_type][] = $rule;
        }

        // Cache the results for 1 hour
        set_transient($cache_key, $grouped_rules, 3600);

        return $grouped_rules;
    }

    /**
     * Update pricing rule
     *
     * @param int $rule_id Rule ID
     * @param array $rule_data Updated rule data
     * @return bool Success status
     */
    public function update_pricing_rule($rule_id, $rule_data) {
        $rule_id = intval($rule_id);
        
        // Sanitize and validate input
        $sanitized_data = $this->sanitize_pricing_rule_data($rule_data);
        if (!$sanitized_data) {
            return false;
        }

        // Get existing rule for cache clearing
        $existing_rule = $this->get_pricing_rule($rule_id);
        if (!$existing_rule) {
            return false;
        }

        $result = $this->wpdb->update(
            $this->pricing_rules_table,
            $sanitized_data,
            array('id' => $rule_id),
            array('%s', '%d', '%s', '%s', '%f', '%d'),
            array('%d')
        );

        if ($result === false) {
            error_log('PBC: Failed to update pricing rule - ' . $this->wpdb->last_error);
            return false;
        }

        // Clear related cache
        $this->clear_pricing_cache($existing_rule->rule_type, $existing_rule->object_id);
        $this->clear_pricing_cache($sanitized_data['rule_type'], $sanitized_data['object_id']);
        
        // Trigger cache invalidation
        do_action('pbc_pricing_rule_changed', $sanitized_data['rule_type'], $sanitized_data['object_id'], $sanitized_data['country_code']);

        return true;
    }

    /**
     * Delete pricing rule
     *
     * @param int $rule_id Rule ID
     * @return bool Success status
     */
    public function delete_pricing_rule($rule_id) {
        $rule_id = intval($rule_id);
        
        // Get existing rule for cache clearing
        $existing_rule = $this->get_pricing_rule($rule_id);
        if (!$existing_rule) {
            return false;
        }

        $result = $this->wpdb->delete(
            $this->pricing_rules_table,
            array('id' => $rule_id),
            array('%d')
        );

        if ($result === false) {
            error_log('PBC: Failed to delete pricing rule - ' . $this->wpdb->last_error);
            return false;
        }

        // Clear related cache
        $this->clear_pricing_cache($existing_rule->rule_type, $existing_rule->object_id);
        
        // Trigger cache invalidation
        do_action('pbc_pricing_rule_changed', $existing_rule->rule_type, $existing_rule->object_id, $existing_rule->country_code);

        return true;
    }

    /**
     * Delete all pricing rules for an object
     *
     * @param string $rule_type Rule type
     * @param int|null $object_id Object ID
     * @return int Number of deleted rules
     */
    public function delete_pricing_rules_by_object($rule_type, $object_id = null) {
        $where = array('rule_type' => $rule_type);
        $where_format = array('%s');

        if ($object_id !== null) {
            $where['object_id'] = $object_id;
            $where_format[] = '%d';
        } else {
            // For global rules, object_id should be NULL
            $result = $this->wpdb->query(
                $this->wpdb->prepare(
                    "DELETE FROM {$this->pricing_rules_table} WHERE rule_type = %s AND object_id IS NULL",
                    $rule_type
                )
            );
        }

        if ($object_id !== null) {
            $result = $this->wpdb->delete(
                $this->pricing_rules_table,
                $where,
                $where_format
            );
        }

        if ($result !== false) {
            $this->clear_pricing_cache($rule_type, $object_id);
        }

        return $result !== false ? $result : 0;
    }

    // ========================================
    // COUNTRY CACHE CRUD OPERATIONS
    // ========================================

    /**
     * Save country detection result to cache
     *
     * @param string $session_id Session ID
     * @param string $ip_address IP address
     * @param string $country_code Country code
     * @param string $detection_method Detection method used
     * @param int $cache_duration Cache duration in seconds
     * @return bool Success status
     */
    public function save_country_cache($session_id, $ip_address, $country_code, $detection_method, $cache_duration = 3600) {
        // Sanitize input
        $session_id = sanitize_text_field($session_id);
        $ip_address = sanitize_text_field($ip_address);
        $country_code = strtoupper(sanitize_text_field($country_code));
        $detection_method = sanitize_text_field($detection_method);
        $cache_duration = intval($cache_duration);

        // Validate country code
        if (strlen($country_code) !== 2) {
            return false;
        }

        // Validate detection method
        $valid_methods = array('ip', 'billing', 'shipping');
        if (!in_array($detection_method, $valid_methods)) {
            return false;
        }

        $expires_at = date('Y-m-d H:i:s', time() + $cache_duration);

        // Use INSERT ... ON DUPLICATE KEY UPDATE for upsert behavior
        $result = $this->wpdb->query(
            $this->wpdb->prepare(
                "INSERT INTO {$this->country_cache_table} 
                (session_id, ip_address, country_code, detection_method, expires_at) 
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                ip_address = VALUES(ip_address),
                country_code = VALUES(country_code),
                detection_method = VALUES(detection_method),
                expires_at = VALUES(expires_at)",
                $session_id, $ip_address, $country_code, $detection_method, $expires_at
            )
        );

        return $result !== false;
    }

    /**
     * Get cached country by session ID
     *
     * @param string $session_id Session ID
     * @return object|null Cache entry or null if not found/expired
     */
    public function get_cached_country($session_id) {
        $session_id = sanitize_text_field($session_id);

        $cache_entry = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->country_cache_table} 
                WHERE session_id = %s AND expires_at > %s",
                $session_id,
                current_time('mysql')
            )
        );

        return $cache_entry ?: null;
    }

    /**
     * Get cached country by IP address
     *
     * @param string $ip_address IP address
     * @return object|null Cache entry or null if not found/expired
     */
    public function get_cached_country_by_ip($ip_address) {
        $ip_address = sanitize_text_field($ip_address);

        $cache_entry = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->country_cache_table} 
                WHERE ip_address = %s AND expires_at > %s 
                ORDER BY created_at DESC LIMIT 1",
                $ip_address,
                current_time('mysql')
            )
        );

        return $cache_entry ?: null;
    }

    /**
     * Delete cached country entry
     *
     * @param string $session_id Session ID
     * @return bool Success status
     */
    public function delete_cached_country($session_id) {
        $session_id = sanitize_text_field($session_id);

        $result = $this->wpdb->delete(
            $this->country_cache_table,
            array('session_id' => $session_id),
            array('%s')
        );

        return $result !== false;
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    /**
     * Sanitize pricing rule data
     *
     * @param array $rule_data Raw rule data
     * @return array|false Sanitized data or false on validation failure
     */
    private function sanitize_pricing_rule_data($rule_data) {
        // Required fields
        $required_fields = array('rule_type', 'country_code', 'adjustment_type', 'adjustment_value');
        foreach ($required_fields as $field) {
            if (!isset($rule_data[$field])) {
                error_log("PBC: Missing required field: {$field}");
                return false;
            }
        }

        // Sanitize and validate rule_type
        $rule_type = sanitize_text_field($rule_data['rule_type']);
        $valid_rule_types = array('global', 'category', 'product');
        if (!in_array($rule_type, $valid_rule_types)) {
            error_log("PBC: Invalid rule_type: {$rule_type}");
            return false;
        }

        // Sanitize and validate object_id
        $object_id = null;
        if (isset($rule_data['object_id']) && $rule_data['object_id'] !== null) {
            $object_id = intval($rule_data['object_id']);
            if ($object_id <= 0) {
                $object_id = null;
            }
        }

        // For global rules, object_id should be null
        if ($rule_type === 'global') {
            $object_id = null;
        }

        // Sanitize and validate country_code
        $country_code = strtoupper(sanitize_text_field($rule_data['country_code']));
        if (strlen($country_code) !== 2) {
            error_log("PBC: Invalid country_code: {$country_code}");
            return false;
        }

        // Sanitize and validate adjustment_type
        $adjustment_type = sanitize_text_field($rule_data['adjustment_type']);
        $valid_adjustment_types = array('fixed', 'percentage');
        if (!in_array($adjustment_type, $valid_adjustment_types)) {
            error_log("PBC: Invalid adjustment_type: {$adjustment_type}");
            return false;
        }

        // Sanitize and validate adjustment_value
        $adjustment_value = floatval($rule_data['adjustment_value']);

        // Sanitize is_active
        $is_active = isset($rule_data['is_active']) ? intval($rule_data['is_active']) : 1;
        $is_active = $is_active ? 1 : 0;

        return array(
            'rule_type' => $rule_type,
            'object_id' => $object_id,
            'country_code' => $country_code,
            'adjustment_type' => $adjustment_type,
            'adjustment_value' => $adjustment_value,
            'is_active' => $is_active
        );
    }

    /**
     * Clear pricing cache for specific rule type and object
     *
     * @param string $rule_type Rule type
     * @param int|null $object_id Object ID
     */
    private function clear_pricing_cache($rule_type, $object_id = null) {
        // Clear WordPress transients
        delete_transient('pbc_pricing_rules_' . $rule_type . '_' . ($object_id ?: 'global'));
        delete_transient('pbc_pricing_rules');

        // Clear WooCommerce product cache if it's a product rule
        if ($rule_type === 'product' && $object_id && function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients($object_id);
        }
    }

    /**
     * Handle database connection errors
     *
     * @param string $operation Operation being performed
     * @return bool Always returns false to indicate failure
     */
    private function handle_db_error($operation) {
        $error_message = $this->wpdb->last_error;
        error_log("PBC Database Error during {$operation}: {$error_message}");
        
        // In the future, we could implement fallback mechanisms here
        // such as using file-based storage or external cache
        
        return false;
    }

    /**
     * Get pricing rules with pagination and filtering
     *
     * @param array $args Query arguments
     * @return array Results with pagination info
     */
    public function get_pricing_rules_paginated($args = array()) {
        $defaults = array(
            'rule_type' => '',
            'country_code' => '',
            'object_id' => '',
            'is_active' => '',
            'per_page' => 20,
            'page' => 1,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );

        $args = wp_parse_args($args, $defaults);

        // Build WHERE clause
        $where_conditions = array();
        $where_values = array();

        if (!empty($args['rule_type'])) {
            $where_conditions[] = "rule_type = %s";
            $where_values[] = $args['rule_type'];
        }

        if (!empty($args['country_code'])) {
            $where_conditions[] = "country_code = %s";
            $where_values[] = $args['country_code'];
        }

        if (!empty($args['object_id'])) {
            $where_conditions[] = "object_id = %d";
            $where_values[] = intval($args['object_id']);
        }

        if ($args['is_active'] !== '') {
            $where_conditions[] = "is_active = %d";
            $where_values[] = intval($args['is_active']);
        }

        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }

        // Get total count
        $count_query = "SELECT COUNT(*) FROM {$this->pricing_rules_table} {$where_clause}";
        if (!empty($where_values)) {
            $count_query = $this->wpdb->prepare($count_query, $where_values);
        }
        $total_items = $this->wpdb->get_var($count_query);

        // Build ORDER BY clause
        $allowed_orderby = array('id', 'rule_type', 'country_code', 'created_at', 'updated_at');
        $orderby = in_array($args['orderby'], $allowed_orderby) ? $args['orderby'] : 'created_at';
        $order = strtoupper($args['order']) === 'ASC' ? 'ASC' : 'DESC';

        // Build LIMIT clause
        $per_page = max(1, intval($args['per_page']));
        $page = max(1, intval($args['page']));
        $offset = ($page - 1) * $per_page;

        // Get results
        $query = "SELECT * FROM {$this->pricing_rules_table} {$where_clause} 
                  ORDER BY {$orderby} {$order} 
                  LIMIT %d OFFSET %d";
        
        $query_values = array_merge($where_values, array($per_page, $offset));
        $results = $this->wpdb->get_results($this->wpdb->prepare($query, $query_values));

        return array(
            'items' => $results ?: array(),
            'total_items' => intval($total_items),
            'per_page' => $per_page,
            'current_page' => $page,
            'total_pages' => ceil($total_items / $per_page)
        );
    }

    /**
     * Get count of active pricing rules
     *
     * @return int Number of active rules
     */
    public function get_active_rules_count() {
        $count = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->pricing_rules_table} WHERE is_active = %d",
                1
            )
        );

        return intval($count);
    }

    /**
     * Get list of countries that have pricing rules
     *
     * @return array Array of country codes
     */
    public function get_countries_in_use() {
        $countries = $this->wpdb->get_col(
            "SELECT DISTINCT country_code FROM {$this->pricing_rules_table} ORDER BY country_code"
        );

        return $countries ?: array();
    }

    /**
     * Get all pricing rules grouped by country
     *
     * @return array Array of rules grouped by country code
     */
    public function get_rules_grouped_by_country() {
        $rules = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->pricing_rules_table} WHERE is_active = %d ORDER BY country_code, rule_type",
                1
            )
        );

        $grouped = array();
        foreach ($rules as $rule) {
            if (!isset($grouped[$rule->country_code])) {
                $grouped[$rule->country_code] = array();
            }
            $grouped[$rule->country_code][] = $rule;
        }

        return $grouped;
    }

    /**
     * Get all pricing rules (for export)
     *
     * @return array Array of all rules
     */
    public function get_all_pricing_rules() {
        $rules = $this->wpdb->get_results(
            "SELECT * FROM {$this->pricing_rules_table} ORDER BY rule_type, object_id, country_code"
        );

        return $rules ?: array();
    }

    /**
     * Get pricing rules for multiple products in batch
     *
     * @param array $product_ids Array of product IDs
     * @param string $country_code Country code
     * @return array Array of rules keyed by product ID
     */
    public function get_batch_pricing_rules($product_ids, $country_code) {
        if (empty($product_ids)) {
            return array();
        }

        // Check cache first
        $cache_key = 'pbc_batch_rules_' . md5(implode(',', $product_ids) . '_' . $country_code);
        $cached_rules = get_transient($cache_key);
        if ($cached_rules !== false) {
            return $cached_rules;
        }

        $product_ids_str = implode(',', array_map('intval', $product_ids));
        
        // Get product-specific rules
        $product_rules = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->pricing_rules_table} 
                WHERE rule_type = 'product' 
                AND object_id IN ({$product_ids_str}) 
                AND country_code = %s 
                AND is_active = 1",
                $country_code
            )
        );

        // Get category rules for products that don't have product-specific rules
        $category_rules = array();
        $products_without_rules = array_diff($product_ids, wp_list_pluck($product_rules, 'object_id'));
        
        if (!empty($products_without_rules)) {
            // Get category IDs for products without rules
            $category_ids = array();
            foreach ($products_without_rules as $product_id) {
                $terms = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));
                if (!is_wp_error($terms) && !empty($terms)) {
                    $category_ids = array_merge($category_ids, $terms);
                }
            }
            
            if (!empty($category_ids)) {
                $category_ids = array_unique($category_ids);
                $category_ids_str = implode(',', array_map('intval', $category_ids));
                
                $category_rules = $this->wpdb->get_results(
                    $this->wpdb->prepare(
                        "SELECT * FROM {$this->pricing_rules_table} 
                        WHERE rule_type = 'category' 
                        AND object_id IN ({$category_ids_str}) 
                        AND country_code = %s 
                        AND is_active = 1",
                        $country_code
                    )
                );
            }
        }

        // Get global rule
        $global_rule = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->pricing_rules_table} 
                WHERE rule_type = 'global' 
                AND object_id IS NULL 
                AND country_code = %s 
                AND is_active = 1",
                $country_code
            )
        );

        // Organize rules by product ID
        $organized_rules = array();
        
        // Add product-specific rules
        foreach ($product_rules as $rule) {
            $organized_rules[$rule->object_id] = $rule;
        }

        // Add category rules for products without product-specific rules
        foreach ($products_without_rules as $product_id) {
            if (!isset($organized_rules[$product_id])) {
                $terms = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));
                if (!is_wp_error($terms) && !empty($terms)) {
                    foreach ($terms as $category_id) {
                        foreach ($category_rules as $rule) {
                            if ($rule->object_id == $category_id) {
                                $organized_rules[$product_id] = $rule;
                                break 2; // Break both loops
                            }
                        }
                    }
                }
            }
        }

        // Add global rule for products without any specific rules
        if ($global_rule) {
            foreach ($product_ids as $product_id) {
                if (!isset($organized_rules[$product_id])) {
                    $organized_rules[$product_id] = $global_rule;
                }
            }
        }

        // Cache the results for 15 minutes
        set_transient($cache_key, $organized_rules, 900);

        return $organized_rules;
    }

    /**
     * Get detection count by method
     *
     * @param string|null $method Detection method filter
     * @return int Detection count
     */
    public function get_detection_count($method = null) {
        $where_clause = '';
        $params = array();

        if ($method) {
            $where_clause = 'WHERE detection_method = %s';
            $params[] = $method;
        }

        $query = "SELECT COUNT(*) FROM {$this->country_cache_table} {$where_clause}";
        if (!empty($params)) {
            $query = $this->wpdb->prepare($query, $params);
        }

        $count = $this->wpdb->get_var($query);
        return intval($count);
    }

    /**
     * Bulk insert pricing rules for better performance
     *
     * @param array $rules Array of rule data arrays
     * @return int Number of rules inserted
     */
    public function bulk_insert_pricing_rules($rules) {
        if (empty($rules)) {
            return 0;
        }

        $values = array();
        $placeholders = array();

        foreach ($rules as $rule) {
            $sanitized = $this->sanitize_pricing_rule_data($rule);
            if (!$sanitized) {
                continue;
            }

            $values[] = $sanitized['rule_type'];
            $values[] = $sanitized['object_id'];
            $values[] = $sanitized['country_code'];
            $values[] = $sanitized['adjustment_type'];
            $values[] = $sanitized['adjustment_value'];
            $values[] = $sanitized['is_active'];

            $placeholders[] = "(%s, %d, %s, %s, %f, %d)";
        }

        if (empty($placeholders)) {
            return 0;
        }

        $sql = "INSERT INTO {$this->pricing_rules_table} 
                (rule_type, object_id, country_code, adjustment_type, adjustment_value, is_active) 
                VALUES " . implode(', ', $placeholders) . "
                ON DUPLICATE KEY UPDATE 
                adjustment_type = VALUES(adjustment_type),
                adjustment_value = VALUES(adjustment_value),
                is_active = VALUES(is_active),
                updated_at = CURRENT_TIMESTAMP";

        $result = $this->wpdb->query($this->wpdb->prepare($sql, $values));

        if ($result !== false) {
            // Clear related caches
            $this->clear_all_pricing_cache();
            return $result;
        }

        return 0;
    }

    /**
     * Bulk delete pricing rules
     *
     * @param array $rule_ids Array of rule IDs to delete
     * @return int Number of rules deleted
     */
    public function bulk_delete_pricing_rules($rule_ids) {
        if (empty($rule_ids)) {
            return 0;
        }

        $rule_ids = array_map('intval', $rule_ids);
        $placeholders = implode(',', array_fill(0, count($rule_ids), '%d'));

        $result = $this->wpdb->query(
            $this->wpdb->prepare(
                "DELETE FROM {$this->pricing_rules_table} WHERE id IN ({$placeholders})",
                $rule_ids
            )
        );

        if ($result !== false) {
            $this->clear_all_pricing_cache();
            return $result;
        }

        return 0;
    }

    /**
     * Clear all pricing-related cache
     */
    private function clear_all_pricing_cache() {
        global $wpdb;
        
        // Clear transients
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE %s 
             OR option_name LIKE %s
             OR option_name LIKE %s",
            '_transient_pbc_rules_%',
            '_transient_pbc_country_rules_%',
            '_transient_pbc_batch_rules_%'
        ));
    }

    /**
     * Get pricing rules with optimized query for multiple products
     *
     * @param array $product_ids Array of product IDs
     * @param array $country_codes Array of country codes
     * @return array Organized rules data
     */
    public function get_optimized_pricing_rules($product_ids, $country_codes) {
        if (empty($product_ids) || empty($country_codes)) {
            return array();
        }

        $product_ids_placeholders = implode(',', array_fill(0, count($product_ids), '%d'));
        $country_codes_placeholders = implode(',', array_fill(0, count($country_codes), '%s'));

        // Single optimized query to get all relevant rules
        $sql = $this->wpdb->prepare("
            SELECT * FROM {$this->pricing_rules_table} 
            WHERE is_active = 1 
            AND country_code IN ({$country_codes_placeholders})
            AND (
                (rule_type = 'product' AND object_id IN ({$product_ids_placeholders}))
                OR (rule_type = 'category' AND object_id IN (
                    SELECT term_taxonomy_id 
                    FROM {$this->wpdb->term_relationships} tr
                    JOIN {$this->wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
                    WHERE tr.object_id IN ({$product_ids_placeholders}) 
                    AND tt.taxonomy = 'product_cat'
                ))
                OR (rule_type = 'global' AND object_id IS NULL)
            )
            ORDER BY 
                FIELD(rule_type, 'product', 'category', 'global'),
                country_code,
                object_id
        ", array_merge($country_codes, $product_ids, $product_ids));

        $rules = $this->wpdb->get_results($sql);

        // Organize rules by product and country
        $organized = array();
        foreach ($product_ids as $product_id) {
            foreach ($country_codes as $country_code) {
                $organized[$product_id][$country_code] = null;
            }
        }

        // Process rules with priority (product > category > global)
        foreach ($rules as $rule) {
            if ($rule->rule_type === 'product') {
                $organized[$rule->object_id][$rule->country_code] = $rule;
            } elseif ($rule->rule_type === 'category') {
                // Apply to products in this category if no product-specific rule exists
                $category_products = get_posts(array(
                    'post_type' => 'product',
                    'posts_per_page' => -1,
                    'fields' => 'ids',
                    'post__in' => $product_ids,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'product_cat',
                            'field' => 'term_id',
                            'terms' => $rule->object_id
                        )
                    )
                ));

                foreach ($category_products as $product_id) {
                    if (!$organized[$product_id][$rule->country_code]) {
                        $organized[$product_id][$rule->country_code] = $rule;
                    }
                }
            } elseif ($rule->rule_type === 'global') {
                // Apply to all products if no specific rule exists
                foreach ($product_ids as $product_id) {
                    if (!$organized[$product_id][$rule->country_code]) {
                        $organized[$product_id][$rule->country_code] = $rule;
                    }
                }
            }
        }

        return $organized;
    }

    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public function get_cache_statistics() {
        global $wpdb;
        
        $cache_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_pbc_%'"
        );

        return array(
            'total_cache_entries' => intval($cache_count),
            'pricing_rules_cache' => $this->get_cache_count('pbc_rules_'),
            'country_rules_cache' => $this->get_cache_count('pbc_country_rules_'),
            'batch_rules_cache' => $this->get_cache_count('pbc_batch_rules_')
        );
    }

    /**
     * Get cache count for specific prefix
     *
     * @param string $prefix Cache key prefix
     * @return int Cache count
     */
    private function get_cache_count($prefix) {
        global $wpdb;
        
        return intval($wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . $prefix . '%'
            )
        ));
    }

    // ========================================
    // API SUPPORT METHODS
    // ========================================

    /**
     * Get applicable pricing rule for a product and country
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return object|null Rule object or null if not found
     */
    public function get_applicable_rule($product_id, $country_code) {
        // Check product-level rule first
        $rule = $this->get_pricing_rule_by_criteria('product', $product_id, $country_code);
        if ($rule) {
            return $rule;
        }

        // Check category-level rules
        $product = wc_get_product($product_id);
        if ($product) {
            $category_ids = $product->get_category_ids();
            foreach ($category_ids as $category_id) {
                $rule = $this->get_pricing_rule_by_criteria('category', $category_id, $country_code);
                if ($rule) {
                    return $rule;
                }
            }
        }

        // Check global rule
        $rule = $this->get_pricing_rule_by_criteria('global', null, $country_code);
        return $rule;
    }

    /**
     * Get all pricing rules for a specific product
     *
     * @param int $product_id Product ID
     * @return array Array of rule objects
     */
    public function get_product_pricing_rules($product_id) {
        $rules = array();

        // Get product-specific rules
        $product_rules = $this->get_pricing_rules_by_type('product', $product_id);
        $rules = array_merge($rules, $product_rules);

        // Get category rules for this product
        $product = wc_get_product($product_id);
        if ($product) {
            $category_ids = $product->get_category_ids();
            foreach ($category_ids as $category_id) {
                $category_rules = $this->get_pricing_rules_by_type('category', $category_id);
                $rules = array_merge($rules, $category_rules);
            }
        }

        // Get global rules
        $global_rules = $this->get_pricing_rules_by_type('global');
        $rules = array_merge($rules, $global_rules);

        return $rules;
    }

    /**
     * Get pricing rules with optional filters
     *
     * @param string|null $rule_type Rule type filter
     * @param int|null $object_id Object ID filter
     * @param string|null $country_code Country code filter
     * @return array Array of rule objects
     */
    public function get_pricing_rules($rule_type = null, $object_id = null, $country_code = null) {
        $where_conditions = array();
        $params = array();

        if ($rule_type) {
            $where_conditions[] = 'rule_type = %s';
            $params[] = $rule_type;
        }

        if ($object_id !== null) {
            $where_conditions[] = 'object_id = %d';
            $params[] = $object_id;
        }

        if ($country_code) {
            $where_conditions[] = 'country_code = %s';
            $params[] = strtoupper($country_code);
        }

        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }

        $sql = "SELECT * FROM {$this->pricing_rules_table} {$where_clause} ORDER BY rule_type, object_id, country_code";
        if (!empty($params)) {
            $sql = $this->wpdb->prepare($sql, $params);
        }

        $rules = $this->wpdb->get_results($sql);
        return $rules ?: array();
    }

    /**
     * Save pricing rule (create or update)
     *
     * @param array $rule_data Rule data
     * @return int|false Rule ID on success, false on failure
     */
    public function save_pricing_rule($rule_data) {
        if (isset($rule_data['id']) && $rule_data['id']) {
            // Update existing rule
            return $this->update_pricing_rule($rule_data['id'], $rule_data) ? $rule_data['id'] : false;
        } else {
            // Create new rule
            return $this->create_pricing_rule($rule_data);
        }
    }
}