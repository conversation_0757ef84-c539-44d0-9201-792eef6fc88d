# Setup Wizard Step Progression Fixes

## Issue Description
The setup wizard was getting stuck at step 2 (Country Detection). When clicking "Continue", it would show "processing" but then reload the same page instead of progressing to step 3 (Sample Rules).

## Root Cause Analysis
The issue was likely caused by:
1. **Form submission handling** - The form submission logic needed better validation
2. **Missing form action** - Forms didn't have explicit action URLs
3. **Nonce verification** - Using deprecated `check_admin_referer()` instead of proper nonce verification
4. **Error handling** - No proper error handling for missing fields or failed validation

## Fixes Applied

### 1. Enhanced Form Submission Handling
**File:** `includes/class-pbc-setup-wizard.php` - `render_setup_page()` method

**Before:**
```php
if (!empty($_POST['save_step']) && isset($this->steps[$this->step]['handler'])) {
    call_user_func($this->steps[$this->step]['handler'], $this);
}
```

**After:**
```php
if (!empty($_POST['save_step']) && isset($this->steps[$this->step]['handler']) && !empty($this->steps[$this->step]['handler'])) {
    // Debug logging
    error_log('PBC Setup: Form submitted for step: ' . $this->step);
    error_log('PBC Setup: Posted step: ' . $_POST['save_step']);
    error_log('PBC Setup: Handler exists: ' . (isset($this->steps[$this->step]['handler']) ? 'yes' : 'no'));
    
    // Verify the step matches the posted step
    if ($_POST['save_step'] === $this->step) {
        error_log('PBC Setup: Calling handler for step: ' . $this->step);
        call_user_func($this->steps[$this->step]['handler'], $this);
        return; // Handler should redirect, so we return here
    } else {
        error_log('PBC Setup: Step mismatch - current: ' . $this->step . ', posted: ' . $_POST['save_step']);
    }
}
```

**Improvements:**
- Added validation to ensure handler exists and is not empty
- Added step matching validation
- Added debug logging to track form submission
- Added early return after handler call

### 2. Improved Nonce Verification and Error Handling
**File:** `includes/class-pbc-setup-wizard.php` - `setup_country_detection_save()` method

**Before:**
```php
public function setup_country_detection_save() {
    check_admin_referer('pbc-setup');

    $settings = get_option('pbc_settings', array());
    
    $settings['country_detection_method'] = sanitize_text_field($_POST['detection_method']);
    $settings['default_country'] = sanitize_text_field($_POST['default_country']);
    $settings['cache_duration'] = absint($_POST['cache_duration']);

    update_option('pbc_settings', $settings);

    wp_redirect(esc_url_raw($this->get_next_step_link()));
    exit;
}
```

**After:**
```php
public function setup_country_detection_save() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['_wpnonce'], 'pbc-setup')) {
        wp_die(__('Security check failed. Please try again.', 'price-by-country'));
    }

    // Verify required fields
    if (empty($_POST['detection_method']) || empty($_POST['default_country'])) {
        wp_die(__('Required fields are missing. Please fill in all required fields.', 'price-by-country'));
    }

    $settings = get_option('pbc_settings', array());
    
    $settings['country_detection_method'] = sanitize_text_field($_POST['detection_method']);
    $settings['default_country'] = sanitize_text_field($_POST['default_country']);
    $settings['cache_duration'] = isset($_POST['cache_duration']) ? absint($_POST['cache_duration']) : 3600;

    update_option('pbc_settings', $settings);

    // Get next step URL
    $next_url = $this->get_next_step_link();
    
    // Debug: Log the next URL
    error_log('PBC Setup: Next step URL: ' . $next_url);
    
    wp_redirect(esc_url_raw($next_url));
    exit;
}
```

**Improvements:**
- Replaced `check_admin_referer()` with proper `wp_verify_nonce()`
- Added field validation with user-friendly error messages
- Added fallback value for cache_duration
- Added debug logging for next step URL
- Better error handling with `wp_die()`

### 3. Added Explicit Form Actions
**Files:** `includes/class-pbc-setup-wizard.php` - Form elements

**Before:**
```php
<form method="post">
```

**After:**
```php
<form method="post" action="<?php echo esc_url(admin_url('admin.php?page=pbc-setup-wizard&step=' . $this->step)); ?>">
```

**Improvements:**
- Added explicit form action URLs
- Ensures forms submit to the correct page and step
- Prevents potential routing issues

### 4. Debug Tools Created
**Files:** `debug-setup-form.php`, `test-admin-integration.php`

Created debugging tools to help identify issues:
- Form submission testing
- POST data inspection
- Nonce validation testing
- Step progression testing
- WordPress compatibility checks

## Testing Instructions

### 1. Enable Debug Logging
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### 2. Test Step Progression
1. Go to **WooCommerce > Setup Wizard**
2. Complete the Country Detection step
3. Click "Continue"
4. Check if it progresses to Sample Rules step
5. Check error log for debug messages

### 3. Check Error Log
Look for messages starting with "PBC Setup:" in your WordPress error log:
- Form submission tracking
- Handler execution
- Next step URL generation
- Any error conditions

### 4. Use Debug Tools
- Access `debug-setup-form.php` in WordPress admin
- Use `test-admin-integration.php` to verify integration
- Check POST data and form submission behavior

## Expected Behavior After Fixes

1. **Form Submission:** Forms should submit properly with validation
2. **Step Progression:** Should advance from Country Detection to Sample Rules
3. **Error Handling:** Clear error messages for validation failures
4. **Debug Information:** Detailed logging for troubleshooting
5. **Security:** Proper nonce verification and field validation

## Rollback Instructions

If issues persist, you can temporarily revert by:
1. Removing debug logging lines
2. Restoring original nonce verification method
3. Removing explicit form actions
4. Checking WordPress and WooCommerce compatibility

## Additional Debugging

If the issue persists:
1. Check WordPress error log for "PBC Setup:" messages
2. Verify WooCommerce is active and user has proper permissions
3. Test with default WordPress theme
4. Disable other plugins temporarily
5. Check for JavaScript errors in browser console

The fixes should resolve the step progression issue and provide better error handling and debugging capabilities.
