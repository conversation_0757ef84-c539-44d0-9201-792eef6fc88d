<?php
/**
 * Debug Setup Form Submission
 * Add this to WordPress admin to debug form submission issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

echo "<h2>Setup Wizard Form Debug</h2>";

// Check if form was submitted
if (!empty($_POST)) {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    // Check nonce
    if (isset($_POST['_wpnonce'])) {
        $nonce_valid = wp_verify_nonce($_POST['_wpnonce'], 'pbc-setup');
        echo "<p>Nonce valid: " . ($nonce_valid ? 'Yes' : 'No') . "</p>";
    }
    
    // Check save_step
    if (isset($_POST['save_step'])) {
        echo "<p>Save step: " . esc_html($_POST['save_step']) . "</p>";
    }
}

// Check current step from URL
$current_step = isset($_GET['step']) ? sanitize_key($_GET['step']) : 'country_detection';
echo "<p>Current step from URL: " . esc_html($current_step) . "</p>";

// Check if setup wizard class exists and get next step
if (class_exists('PBC_Setup_Wizard')) {
    $wizard = new PBC_Setup_Wizard();
    
    // Get steps
    $reflection = new ReflectionClass($wizard);
    $steps_property = $reflection->getProperty('steps');
    $steps_property->setAccessible(true);
    $steps = $steps_property->getValue($wizard);
    
    echo "<h3>Available Steps:</h3>";
    echo "<ul>";
    foreach ($steps as $step_key => $step_data) {
        echo "<li><strong>" . esc_html($step_key) . "</strong>: " . esc_html($step_data['name']) . "</li>";
    }
    echo "</ul>";
    
    // Test next step link
    $step_property = $reflection->getProperty('step');
    $step_property->setAccessible(true);
    $step_property->setValue($wizard, $current_step);
    
    $next_link = $wizard->get_next_step_link();
    echo "<p>Next step link: <a href='" . esc_url($next_link) . "'>" . esc_html($next_link) . "</a></p>";
}

// Simple test form
?>
<h3>Test Form:</h3>
<form method="post" action="">
    <table class="form-table">
        <tr>
            <th><label for="detection_method">Detection Method:</label></th>
            <td>
                <select name="detection_method" id="detection_method">
                    <option value="auto">Automatic</option>
                    <option value="ip">IP Address</option>
                </select>
            </td>
        </tr>
        <tr>
            <th><label for="default_country">Default Country:</label></th>
            <td>
                <select name="default_country" id="default_country">
                    <option value="US">United States</option>
                    <option value="GB">United Kingdom</option>
                </select>
            </td>
        </tr>
        <tr>
            <th><label for="cache_duration">Cache Duration:</label></th>
            <td>
                <select name="cache_duration" id="cache_duration">
                    <option value="3600">1 hour</option>
                    <option value="7200">2 hours</option>
                </select>
            </td>
        </tr>
    </table>
    
    <p>
        <button type="submit" name="save_step" value="<?php echo esc_attr($current_step); ?>" class="button button-primary">
            Test Submit
        </button>
    </p>
    <?php wp_nonce_field('pbc-setup'); ?>
</form>

<h3>Debug Info:</h3>
<ul>
    <li>WordPress Version: <?php echo get_bloginfo('version'); ?></li>
    <li>PHP Version: <?php echo PHP_VERSION; ?></li>
    <li>Current User Can Manage WooCommerce: <?php echo current_user_can('manage_woocommerce') ? 'Yes' : 'No'; ?></li>
    <li>WooCommerce Active: <?php echo class_exists('WooCommerce') ? 'Yes' : 'No'; ?></li>
</ul>

<h3>Error Log Check:</h3>
<p>Check your WordPress error log for messages starting with "PBC Setup:" to see debugging information.</p>
<p>Error log location: <?php echo ini_get('error_log') ?: 'Check wp-config.php for WP_DEBUG_LOG setting'; ?></p>
