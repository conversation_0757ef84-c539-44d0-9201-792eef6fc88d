<?php
/**
 * Test Step Navigation
 * Add this to WordPress admin to test step navigation logic
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

echo "<h2>Setup Wizard Step Navigation Test</h2>";

// Test the setup wizard step navigation
if (class_exists('PBC_Setup_Wizard')) {
    $wizard = new PBC_Setup_Wizard();
    
    // Use reflection to access private properties and methods
    $reflection = new ReflectionClass($wizard);
    
    // Get steps
    $steps_property = $reflection->getProperty('steps');
    $steps_property->setAccessible(true);
    $steps = $steps_property->getValue($wizard);
    
    echo "<h3>Available Steps:</h3>";
    echo "<ol>";
    foreach ($steps as $step_key => $step_data) {
        echo "<li><strong>" . esc_html($step_key) . "</strong>: " . esc_html($step_data['name']);
        echo " (Handler: " . (empty($step_data['handler']) ? 'none' : 'yes') . ")</li>";
    }
    echo "</ol>";
    
    // Test step navigation for each step
    echo "<h3>Step Navigation Test:</h3>";
    $step_property = $reflection->getProperty('step');
    $step_property->setAccessible(true);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Current Step</th><th>Next Step URL</th><th>Expected Next Step</th></tr>";
    
    $step_keys = array_keys($steps);
    foreach ($step_keys as $index => $step_key) {
        $step_property->setValue($wizard, $step_key);
        $next_url = $wizard->get_next_step_link();
        
        // Parse the URL to get the step parameter
        $url_parts = parse_url($next_url);
        parse_str($url_parts['query'] ?? '', $query_params);
        $next_step = $query_params['step'] ?? 'dashboard';
        
        $expected_next = ($index < count($step_keys) - 1) ? $step_keys[$index + 1] : 'dashboard';
        
        echo "<tr>";
        echo "<td>" . esc_html($step_key) . "</td>";
        echo "<td><a href='" . esc_url($next_url) . "'>" . esc_html($next_step) . "</a></td>";
        echo "<td>" . esc_html($expected_next) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test current step detection
    echo "<h3>Current Step Detection:</h3>";
    $current_step = isset($_GET['step']) ? sanitize_key($_GET['step']) : 'welcome';
    echo "<p>Current step from URL: <strong>" . esc_html($current_step) . "</strong></p>";
    
    // Test form submission detection
    echo "<h3>Form Submission Test:</h3>";
    if (!empty($_POST)) {
        echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
        echo "<h4>POST Data Received:</h4>";
        echo "<pre>" . esc_html(print_r($_POST, true)) . "</pre>";
        
        if (isset($_POST['save_step'])) {
            echo "<p><strong>Save Step:</strong> " . esc_html($_POST['save_step']) . "</p>";
            
            // Test if handler exists
            if (isset($steps[$_POST['save_step']]['handler'])) {
                $handler = $steps[$_POST['save_step']]['handler'];
                echo "<p><strong>Handler:</strong> " . (empty($handler) ? 'Empty' : 'Exists') . "</p>";
            }
        }
        echo "</div>";
    } else {
        echo "<p>No POST data received.</p>";
    }
    
    // Simple test form
    echo "<h3>Test Form:</h3>";
    echo "<form method='post' action='" . esc_url(admin_url('admin.php?page=pbc-setup-wizard&step=country_detection')) . "'>";
    echo "<p>";
    echo "<select name='detection_method'>";
    echo "<option value='auto'>Automatic</option>";
    echo "<option value='ip'>IP Address</option>";
    echo "</select>";
    echo "</p>";
    echo "<p>";
    echo "<select name='default_country'>";
    echo "<option value='US'>United States</option>";
    echo "<option value='GB'>United Kingdom</option>";
    echo "</select>";
    echo "</p>";
    echo "<p>";
    echo "<select name='cache_duration'>";
    echo "<option value='3600'>1 hour</option>";
    echo "<option value='7200'>2 hours</option>";
    echo "</select>";
    echo "</p>";
    echo "<p>";
    echo "<button type='submit' name='save_step' value='country_detection' class='button button-primary'>Test Submit</button>";
    echo "</p>";
    wp_nonce_field('pbc-setup');
    echo "</form>";
    
} else {
    echo "<p>PBC_Setup_Wizard class not found!</p>";
}

// Check if debug logging is enabled
echo "<h3>Debug Status:</h3>";
echo "<ul>";
echo "<li>WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . "</li>";
echo "<li>WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . "</li>";
echo "<li>Error Log: " . (ini_get('error_log') ?: 'Default location') . "</li>";
echo "</ul>";

echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Enable debug logging in wp-config.php if not already enabled</li>";
echo "<li>Try the test form above</li>";
echo "<li>Check your error log for 'PBC Setup:' messages</li>";
echo "<li>Compare the step navigation URLs with expected values</li>";
echo "</ol>";
